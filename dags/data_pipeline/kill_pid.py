# coding=utf-8
import asyncio
import os

import yaml
from dependency_injector.containers import DynamicContainer
from dependency_injector.wiring import inject, Provide
from rich import print
from sqlalchemy import exc, text

# from dags.data_pipeline.containers import ApplicationContainer
from dags.data_pipeline.containers import <PERSON><PERSON>ontainer, KeePassContainer


async def detect_deadlocks(
        db_container: DynamicContainer = Provide[DatabaseContainer],
):
    """
    Detects deadlocks in the PostgreSQL database by analyzing pg_locks and pg_stat_activity.
    Returns a list of PIDs of transactions involved in deadlocks.
    """
    print(f"started detect_deadlocks")
    print(f"db_container = {type(db_container)}")

    try:

        async with db_container.async_session_managers()["public"].async_session() as pg_async_session:

            query = text("""
                WITH blocked_locks AS (
                    SELECT
                        l1.locktype, l1.database, l1.relation, l1.page, l1.tuple,
                        l1.virtualxid, l1.transactionid, l1.classid, l1.objid, l1.objsubid,
                        l1.pid AS blocked_pid, l1.mode AS blocked_mode,
                        l2.pid AS blocking_pid, l2.mode AS blocking_mode
                    FROM
                        pg_locks l1
                    JOIN
                        pg_locks l2
                    ON
                        l1.locktype = l2.locktype
                        AND l1.database IS NOT DISTINCT FROM l2.database
                        AND l1.relation IS NOT DISTINCT FROM l2.relation
                        AND l1.page IS NOT DISTINCT FROM l2.page
                        AND l1.tuple IS NOT DISTINCT FROM l2.tuple
                        AND l1.virtualxid IS NOT DISTINCT FROM l2.virtualxid
                        AND l1.transactionid IS NOT DISTINCT FROM l2.transactionid
                        AND l1.classid IS NOT DISTINCT FROM l2.classid
                        AND l1.objid IS NOT DISTINCT FROM l2.objid
                        AND l1.objsubid IS NOT DISTINCT FROM l2.objsubid
                    WHERE
                        l1.granted = false AND l2.granted = true
                )
                SELECT DISTINCT
                    blocked_pid
                FROM
                    blocked_locks
                WHERE
                    blocked_pid <> blocking_pid;
            """)

            result = await pg_async_session.execute(query)
            rows = [row[0] for row in result.fetchall()]
            print(f"rows returned = {rows}")
            return rows
    except exc.SQLAlchemyError as e:
        print(f"Error detecting deadlocks: {e}")
        return []


async def monitor_deadlocks(interval: int = 5,):
    """
    Monitors and resolves deadlocks periodically.
    """
    while True:
        # Detect deadlocked transactions
        deadlocked_pids = await detect_deadlocks()
        if deadlocked_pids:
            print(f"Detected deadlocks: {deadlocked_pids}")
            for pid in deadlocked_pids:
                # Terminate deadlocked transactions
                await kill_transaction(pid)
                pass
        await asyncio.sleep(interval)  # Wait before next check


@inject
async def kill_transaction(
        pid: int,
        db_container: DynamicContainer = Provide[DatabaseContainer],
):
    """
    Kills a transaction in PostgreSQL using pg_terminate_backend.
    """
    try:
        query = text(f"SELECT pg_terminate_backend(:pid)")
        async with db_container.async_session_managers()["public"].async_session() as pg_async_session:
            await pg_async_session.execute(query, {'pid': pid})
        print(f"Terminated transaction with PID: {pid}")
    except exc.SQLAlchemyError as e:
        print(f"Error terminating transaction {pid}: {e}")

if __name__ == "__main__":

    with open("./dags/data_pipeline/config.yaml", 'r') as f:
        config_data = yaml.safe_load(f)
    print(config_data)
    keepass_container = KeePassContainer()
    print(__name__)
    keepass_container.wire(modules=[__name__])
    print(f"keepass_container = {type(keepass_container)}")
    print(keepass_container.keepass_manager())

    container = DatabaseContainer()
    container.wire(modules=[__name__])

    print(f"container = {type(container)}")

    print(container.plat_session_manager())

    try:
        asyncio.run(monitor_deadlocks())
    except KeyboardInterrupt as err:
        print(f"request requested cancel")
        exit(0)
    except Exception as e:
        print(e)
